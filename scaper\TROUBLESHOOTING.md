# 故障排除指南

## 问题解决方案

### 1. MCP SSE 连接初始化错误

**问题描述:**
```
RuntimeError: Received request before initialization was complete
ExceptionGroup: unhandled errors in a TaskGroup
anyio.BrokenResourceError
```

**原因分析:**
- MCP SSE 连接在服务器完全初始化前就收到了客户端请求
- 客户端连接过快，导致连接资源被破坏

**解决方案:**

#### 方案1: 使用简化版服务器
我们提供了一个更稳定的简化版服务器：

```bash
# 启动简化版服务器
uv run python simple_server.py

# 测试简化版服务器
uv run python test_simple.py
```

简化版服务器特点：
- 更简单的初始化流程
- 减少了复杂的依赖
- 专注于核心网页爬取功能

#### 方案2: 客户端重试机制
在客户端代码中添加重试逻辑：

```python
async def test_with_retry():
    max_retries = 3
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            client = Client("http://127.0.0.1:8001/sse")
            async with client:
                await asyncio.sleep(1)  # 等待连接稳定
                # 执行测试...
                return True
        except Exception as e:
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
            else:
                return False
```

#### 方案3: 修改端口配置
如果遇到端口占用问题：

```bash
# 修改 .env 文件中的端口
MCP_SERVER_PORT=8001  # 或其他可用端口
```

### 2. 端口占用问题

**问题描述:**
```
ERROR: [Errno 10048] error while attempting to bind on address ('127.0.0.1', 8000): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
```

**解决方案:**
1. 修改 `.env` 文件中的端口号
2. 或者停止占用端口的进程
3. 使用 `netstat -ano | findstr :8000` 查看端口占用情况

### 3. API 密钥配置问题

**问题描述:**
图片分析功能不可用

**解决方案:**
1. 配置火山引擎豆包模型 API 密钥：
   ```env
   DOUBAO_API_KEY=your_actual_api_key_here
   ```

2. 或者使用不需要 API 密钥的基础功能：
   ```python
   # 只进行网页爬取，不分析图片
   result = await client.call_tool("scrape_basic", {
       "url": "https://example.com"
   })
   ```

### 4. Playwright 浏览器问题

**问题描述:**
网页爬取失败，浏览器相关错误

**解决方案:**
```bash
# 重新安装 Playwright 浏览器
uv run playwright install chromium

# 或者安装所有浏览器
uv run playwright install
```

## 推荐使用方式

### 开发和测试阶段
使用简化版服务器进行开发和测试：

```bash
# 1. 启动简化版服务器
uv run python simple_server.py

# 2. 在另一个终端测试
uv run python test_simple.py
```

### 生产环境
配置完整的 API 密钥后使用完整版服务器：

```bash
# 1. 配置 .env 文件
cp .env.example .env
# 编辑 .env 文件，配置 DOUBAO_API_KEY

# 2. 启动完整版服务器
uv run python server.py

# 3. 测试完整功能
uv run python test_client.py
```

## 可用的服务器版本

### 1. 简化版服务器 (`simple_server.py`)
- ✅ 基础网页爬取
- ✅ 文本和链接提取
- ✅ 稳定的 SSE 连接
- ❌ 不支持图片分析

**工具:**
- `test_connection()` - 测试连接
- `get_server_info()` - 获取服务器信息
- `scrape_basic(url)` - 基础网页爬取

### 2. 完整版服务器 (`server.py`)
- ✅ 完整网页爬取功能
- ✅ 图片内容分析
- ✅ 批量图片处理
- ⚠️ 需要 API 密钥配置

**工具:**
- `scrape_webpage_tool()` - 完整网页爬取
- `analyze_webpage_images()` - 网页图片分析
- `analyze_image_url()` - 单张图片分析
- `get_server_status()` - 服务器状态

## 测试文件说明

- `test_simple.py` - 测试简化版服务器
- `test_client.py` - 测试完整版服务器
- `test_basic.py` - 测试基础爬虫功能
- `client_example.py` - 客户端使用示例

## 常见问题

**Q: 为什么会有两个服务器版本？**
A: 简化版服务器更稳定，适合开发测试；完整版服务器功能更全，适合生产使用。

**Q: 如何选择使用哪个版本？**
A: 如果只需要基础网页爬取，使用简化版；如果需要图片分析，使用完整版。

**Q: 可以同时运行两个服务器吗？**
A: 可以，但需要使用不同的端口。

**Q: 如何调试连接问题？**
A: 查看服务器日志，使用重试机制，确保端口未被占用。
