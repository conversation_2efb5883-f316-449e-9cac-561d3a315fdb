"""网页爬虫模块 - 使用 Playwright"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
import aiohttp

from playwright.async_api import async_playwright, Browser, Page
from bs4 import BeautifulSoup

from config import config
from image_analyzer import image_analyzer

logger = logging.getLogger(__name__)


class WebScraper:
    """网页爬虫类"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.playwright = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=config.PLAYWRIGHT_HEADLESS
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
    
    async def scrape_webpage(
        self, 
        url: str, 
        include_images: bool = True,
        analyze_images: bool = False,
        image_analysis_prompt: str = "请描述这张图片的内容"
    ) -> Dict[str, Any]:
        """
        爬取网页内容
        
        Args:
            url: 目标网页URL
            include_images: 是否包含图片信息
            analyze_images: 是否分析图片内容
            image_analysis_prompt: 图片分析提示词
            
        Returns:
            包含网页内容的字典
        """
        try:
            if not self.browser:
                raise RuntimeError("Browser not initialized. Use async context manager.")
            
            page = await self.browser.new_page()
            
            # 设置超时时间
            page.set_default_timeout(config.PLAYWRIGHT_TIMEOUT)
            
            # 访问页面
            await page.goto(url, wait_until="networkidle")
            
            # 获取基本信息
            title = await page.title()
            content = await page.content()
            
            # 解析HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 提取文本内容
            text_content = self._extract_text_content(soup)
            
            # 提取链接
            links = self._extract_links(soup, url)
            
            result = {
                "url": url,
                "title": title,
                "text_content": text_content,
                "links": links,
                "status": "success"
            }
            
            # 处理图片
            if include_images:
                images_info = await self._extract_images(page, soup, url)
                result["images"] = images_info
                
                # 分析图片内容
                if analyze_images and images_info:
                    image_analyses = await self._analyze_page_images(
                        images_info, image_analysis_prompt
                    )
                    result["image_analyses"] = image_analyses
            
            await page.close()
            return result
            
        except Exception as e:
            logger.error(f"Scraping error for {url}: {str(e)}")
            return {
                "url": url,
                "status": "error",
                "error": str(e)
            }
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """提取网页文本内容"""
        # 移除脚本和样式标签
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 获取文本内容
        text = soup.get_text()
        
        # 清理文本
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """提取网页链接"""
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 转换为绝对URL
            absolute_url = urljoin(base_url, href)
            
            links.append({
                "url": absolute_url,
                "text": text,
                "href": href
            })
        
        return links
    
    async def _extract_images(
        self, 
        page: Page, 
        soup: BeautifulSoup, 
        base_url: str
    ) -> List[Dict[str, Any]]:
        """提取网页图片信息"""
        images = []
        
        # 从HTML中提取图片标签
        img_tags = soup.find_all('img', src=True)
        
        for img in img_tags:
            src = img['src']
            alt = img.get('alt', '')
            
            # 转换为绝对URL
            absolute_url = urljoin(base_url, src)
            
            image_info = {
                "src": absolute_url,
                "alt": alt,
                "original_src": src
            }
            
            # 尝试获取图片尺寸
            try:
                width = img.get('width')
                height = img.get('height')
                if width:
                    image_info["width"] = width
                if height:
                    image_info["height"] = height
            except:
                pass
            
            images.append(image_info)
        
        return images
    
    async def _analyze_page_images(
        self, 
        images_info: List[Dict[str, Any]], 
        prompt: str
    ) -> List[Dict[str, Any]]:
        """分析页面中的图片"""
        analyses = []
        
        for i, img_info in enumerate(images_info):
            try:
                # 下载图片
                image_data = await self._download_image(img_info["src"])
                if image_data:
                    # 分析图片
                    analysis = await image_analyzer.analyze_image(image_data, prompt)
                    analyses.append({
                        "image_index": i,
                        "image_src": img_info["src"],
                        "analysis": analysis
                    })
                else:
                    analyses.append({
                        "image_index": i,
                        "image_src": img_info["src"],
                        "analysis": "无法下载图片"
                    })
            except Exception as e:
                logger.error(f"Error analyzing image {img_info['src']}: {str(e)}")
                analyses.append({
                    "image_index": i,
                    "image_src": img_info["src"],
                    "analysis": f"分析失败: {str(e)}"
                })
        
        return analyses
    
    async def _download_image(self, url: str) -> Optional[bytes]:
        """下载图片数据"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        logger.warning(f"Failed to download image {url}: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error downloading image {url}: {str(e)}")
            return None


# 便捷函数
async def scrape_webpage(
    url: str, 
    include_images: bool = True,
    analyze_images: bool = False,
    image_analysis_prompt: str = "请描述这张图片的内容"
) -> Dict[str, Any]:
    """便捷的网页爬取函数"""
    async with WebScraper() as scraper:
        return await scraper.scrape_webpage(
            url, include_images, analyze_images, image_analysis_prompt
        )
