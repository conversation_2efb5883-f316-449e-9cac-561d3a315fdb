"""MCP SSE 服务器 - 通用网页爬虫与图片解读"""
import logging
import asyncio
from typing import Dict, Any, List

from fastmcp import FastMCP
from config import config
from scraper import scrape_webpage
from image_analyzer import image_analyzer

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 MCP 服务器实例
mcp = FastMCP(
    name="Web Scraper with Image Analysis",
    instructions="""
    这是一个功能强大的网页爬虫和图片分析服务器，具备以下功能：

    🕷️ 网页爬虫功能：
    1. 爬取网页内容（文本、链接、图片）
    2. 提取结构化数据
    3. 支持动态网页处理

    🖼️ 图片分析功能：
    1. 使用火山引擎豆包模型进行AI图片分析
    2. 支持单张图片分析
    3. 支持批量图片处理
    4. 支持多问题图片分析
    5. 自定义分析提示词

    🛠️ 可用工具：
    - scrape_webpage_tool: 完整网页爬取（可选图片分析）
    - analyze_webpage_images: 专门分析网页中的所有图片
    - analyze_image_url: 分析指定URL的单张图片
    - analyze_multiple_image_urls: 批量分析多个图片URL
    - analyze_image_with_custom_questions: 使用自定义问题分析图片
    - get_server_status: 获取服务器状态和配置信息

    📝 使用说明：
    - 基础网页爬取功能无需配置即可使用
    - 图片分析功能需要配置火山引擎豆包模型的API密钥
    - 在 .env 文件中设置 DOUBAO_API_KEY 以启用图片分析功能

    🚀 特色功能：
    - 高性能异步处理
    - 智能错误处理和重试
    - 详细的分析结果和元数据
    - 支持多种图片格式
    - 灵活的分析提示词定制
    """
)


@mcp.tool()
async def scrape_webpage_tool(
    url: str,
    include_images: bool = True,
    analyze_images: bool = False,
    image_analysis_prompt: str = "请详细描述这张图片的内容，包括主要元素、颜色、场景等"
) -> Dict[str, Any]:
    """
    爬取网页内容，可选择是否分析图片

    Args:
        url: 要爬取的网页URL
        include_images: 是否包含图片信息（默认True）
        analyze_images: 是否使用AI分析图片内容（默认False）
        image_analysis_prompt: 图片分析的提示词

    Returns:
        包含网页内容的详细信息
    """
    try:
        logger.info(f"开始爬取网页: {url}")

        # 检查 API 密钥配置
        if analyze_images and (not config.DOUBAO_API_KEY or config.DOUBAO_API_KEY == "your_doubao_api_key_here"):
            logger.warning("图片分析功能需要配置 DOUBAO_API_KEY，将跳过图片分析")
            analyze_images = False

        result = await scrape_webpage(
            url=url,
            include_images=include_images,
            analyze_images=analyze_images,
            image_analysis_prompt=image_analysis_prompt
        )

        logger.info(f"网页爬取完成: {url}, 状态: {result.get('status')}")
        return result

    except Exception as e:
        logger.error(f"网页爬取失败: {url}, 错误: {str(e)}")
        return {
            "url": url,
            "status": "error",
            "error": f"爬取失败: {str(e)}"
        }


@mcp.tool()
async def analyze_webpage_images(
    url: str,
    analysis_prompt: str = "请详细描述这张图片的内容，包括主要对象、场景、颜色和任何文字信息"
) -> Dict[str, Any]:
    """
    专门用于分析网页中所有图片的工具

    Args:
        url: 要分析的网页URL
        analysis_prompt: 图片分析提示词

    Returns:
        包含所有图片分析结果的信息
    """
    try:
        logger.info(f"开始分析网页图片: {url}")

        # 检查 API 密钥配置
        if not config.DOUBAO_API_KEY or config.DOUBAO_API_KEY == "your_doubao_api_key_here":
            return {
                "url": url,
                "status": "error",
                "error": "图片分析功能需要配置 DOUBAO_API_KEY，请在 .env 文件中设置有效的 API 密钥"
            }

        # 先爬取网页获取图片信息
        scrape_result = await scrape_webpage(
            url=url,
            include_images=True,
            analyze_images=True,
            image_analysis_prompt=analysis_prompt
        )

        if scrape_result.get("status") == "error":
            return scrape_result

        # 提取图片分析结果
        image_analyses = scrape_result.get("image_analyses", [])
        images_info = scrape_result.get("images", [])

        result = {
            "url": url,
            "title": scrape_result.get("title", ""),
            "total_images": len(images_info),
            "analyzed_images": len(image_analyses),
            "image_analyses": image_analyses,
            "images_info": images_info,  # 添加图片基本信息
            "status": "success"
        }

        logger.info(f"图片分析完成: {url}, 分析了 {len(image_analyses)} 张图片")
        return result

    except Exception as e:
        logger.error(f"图片分析失败: {url}, 错误: {str(e)}")
        return {
            "url": url,
            "status": "error",
            "error": f"图片分析失败: {str(e)}"
        }


@mcp.tool()
async def analyze_image_url(
    image_url: str,
    analysis_prompt: str = "请详细描述这张图片的内容"
) -> Dict[str, Any]:
    """
    分析指定URL的单张图片

    Args:
        image_url: 图片的URL地址
        analysis_prompt: 分析提示词

    Returns:
        图片分析结果
    """
    try:
        logger.info(f"开始分析图片: {image_url}")

        # 检查 API 密钥配置
        if not config.DOUBAO_API_KEY or config.DOUBAO_API_KEY == "your_doubao_api_key_here":
            return {
                "image_url": image_url,
                "status": "error",
                "error": "图片分析功能需要配置 DOUBAO_API_KEY，请在 .env 文件中设置有效的 API 密钥"
            }

        # 下载图片
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status != 200:
                    return {
                        "image_url": image_url,
                        "status": "error",
                        "error": f"无法下载图片，HTTP状态码: {response.status}"
                    }

                image_data = await response.read()
                content_type = response.headers.get('content-type', 'unknown')
                content_length = len(image_data)

        # 分析图片
        analysis_result = await image_analyzer.analyze_image(image_data, analysis_prompt)

        result = {
            "image_url": image_url,
            "analysis": analysis_result,
            "image_info": {
                "content_type": content_type,
                "size_bytes": content_length,
                "size_kb": round(content_length / 1024, 2)
            },
            "status": "success"
        }

        logger.info(f"图片分析完成: {image_url}")
        return result

    except Exception as e:
        logger.error(f"图片分析失败: {image_url}, 错误: {str(e)}")
        return {
            "image_url": image_url,
            "status": "error",
            "error": f"分析失败: {str(e)}"
        }


@mcp.tool()
async def analyze_multiple_image_urls(
    image_urls: List[str],
    analysis_prompt: str = "请详细描述这张图片的内容"
) -> Dict[str, Any]:
    """
    批量分析多个图片URL

    Args:
        image_urls: 图片URL列表
        analysis_prompt: 分析提示词

    Returns:
        批量图片分析结果
    """
    try:
        logger.info(f"开始批量分析 {len(image_urls)} 张图片")

        # 检查 API 密钥配置
        if not config.DOUBAO_API_KEY or config.DOUBAO_API_KEY == "your_doubao_api_key_here":
            return {
                "total_images": len(image_urls),
                "status": "error",
                "error": "图片分析功能需要配置 DOUBAO_API_KEY，请在 .env 文件中设置有效的 API 密钥"
            }

        # 并发分析所有图片
        tasks = []
        for i, image_url in enumerate(image_urls):
            task = analyze_image_url(image_url, f"{analysis_prompt} (图片 {i+1})")
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        successful_analyses = []
        failed_analyses = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_analyses.append({
                    "index": i,
                    "image_url": image_urls[i],
                    "error": str(result)
                })
            elif result.get("status") == "success":
                successful_analyses.append({
                    "index": i,
                    "image_url": result["image_url"],
                    "analysis": result["analysis"],
                    "image_info": result.get("image_info", {})
                })
            else:
                failed_analyses.append({
                    "index": i,
                    "image_url": image_urls[i],
                    "error": result.get("error", "未知错误")
                })

        final_result = {
            "total_images": len(image_urls),
            "successful_count": len(successful_analyses),
            "failed_count": len(failed_analyses),
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "status": "success"
        }

        logger.info(f"批量图片分析完成: 成功 {len(successful_analyses)}, 失败 {len(failed_analyses)}")
        return final_result

    except Exception as e:
        logger.error(f"批量图片分析失败: {str(e)}")
        return {
            "total_images": len(image_urls),
            "status": "error",
            "error": f"批量分析失败: {str(e)}"
        }


@mcp.tool()
async def analyze_image_with_custom_questions(
    image_url: str,
    questions: List[str]
) -> Dict[str, Any]:
    """
    使用自定义问题列表分析图片

    Args:
        image_url: 图片URL
        questions: 问题列表

    Returns:
        针对每个问题的分析结果
    """
    try:
        logger.info(f"开始多问题分析图片: {image_url}")

        # 检查 API 密钥配置
        if not config.DOUBAO_API_KEY or config.DOUBAO_API_KEY == "your_doubao_api_key_here":
            return {
                "image_url": image_url,
                "status": "error",
                "error": "图片分析功能需要配置 DOUBAO_API_KEY，请在 .env 文件中设置有效的 API 密钥"
            }

        # 下载图片一次
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status != 200:
                    return {
                        "image_url": image_url,
                        "status": "error",
                        "error": f"无法下载图片，HTTP状态码: {response.status}"
                    }

                image_data = await response.read()

        # 对每个问题进行分析
        analyses = []
        for i, question in enumerate(questions):
            try:
                analysis_result = await image_analyzer.analyze_image(image_data, question)
                analyses.append({
                    "question_index": i,
                    "question": question,
                    "answer": analysis_result
                })
            except Exception as e:
                analyses.append({
                    "question_index": i,
                    "question": question,
                    "answer": f"分析失败: {str(e)}"
                })

        result = {
            "image_url": image_url,
            "total_questions": len(questions),
            "analyses": analyses,
            "status": "success"
        }

        logger.info(f"多问题图片分析完成: {image_url}, {len(questions)} 个问题")
        return result

    except Exception as e:
        logger.error(f"多问题图片分析失败: {image_url}, 错误: {str(e)}")
        return {
            "image_url": image_url,
            "status": "error",
            "error": f"分析失败: {str(e)}"
        }


@mcp.tool()
async def get_server_status() -> Dict[str, Any]:
    """
    获取服务器状态信息
    
    Returns:
        服务器状态和配置信息
    """
    try:
        # 验证配置
        config.validate()

        # 检查图片分析功能可用性
        image_analysis_available = bool(config.DOUBAO_API_KEY and config.DOUBAO_API_KEY != "your_doubao_api_key_here")

        return {
            "status": "healthy",
            "server_name": "Web Scraper with Image Analysis",
            "version": "2.0.0",
            "features": [
                "网页内容爬取",
                "图片信息提取",
                "AI图片内容分析",
                "批量图片处理",
                "多问题图片分析",
                "自定义分析提示词"
            ],
            "tools": [
                "scrape_webpage_tool - 完整网页爬取",
                "analyze_webpage_images - 网页图片分析",
                "analyze_image_url - 单张图片分析",
                "analyze_multiple_image_urls - 批量图片分析",
                "analyze_image_with_custom_questions - 多问题图片分析",
                "get_server_status - 服务器状态"
            ],
            "image_analysis": {
                "available": image_analysis_available,
                "model": config.DOUBAO_MODEL if image_analysis_available else "未配置",
                "api_configured": image_analysis_available
            },
            "config": {
                "model": config.DOUBAO_MODEL,
                "headless_browser": config.PLAYWRIGHT_HEADLESS,
                "timeout": config.PLAYWRIGHT_TIMEOUT,
                "host": config.MCP_SERVER_HOST,
                "port": config.MCP_SERVER_PORT
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


if __name__ == "__main__":
    try:
        # 验证配置
        config.validate()
        logger.info("配置验证通过")
        
        # 启动服务器
        logger.info(f"启动 MCP SSE 服务器，地址: {config.MCP_SERVER_HOST}:{config.MCP_SERVER_PORT}")
        mcp.run(
            transport="sse",
            host=config.MCP_SERVER_HOST,
            port=config.MCP_SERVER_PORT
        )
        
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        raise
