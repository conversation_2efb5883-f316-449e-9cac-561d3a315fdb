"""简单的 MCP 客户端测试"""
import asyncio
import time
from fastmcp import Client


async def test_with_retry():
    """带重试机制的测试"""
    max_retries = 3
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            print(f"尝试连接服务器 (第 {attempt + 1} 次)...")
            
            # 连接到 SSE 服务器
            client = Client("http://127.0.0.1:8000/sse")
            
            async with client:
                print("✅ 成功连接到服务器")
                
                # 等待一下确保连接稳定
                await asyncio.sleep(1)
                
                # 测试服务器状态
                print("测试服务器状态...")
                status_result = await client.call_tool("get_server_status", {})
                print("✅ 服务器状态检查成功")
                
                # 测试基础网页爬取
                print("测试基础网页爬取...")
                scrape_result = await client.call_tool("scrape_webpage_tool", {
                    "url": "https://httpbin.org/html",
                    "include_images": True,
                    "analyze_images": False
                })
                print("✅ 网页爬取测试成功")
                
                print("\n🎉 所有测试通过！")
                return True
                
        except Exception as e:
            print(f"❌ 第 {attempt + 1} 次尝试失败: {str(e)}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
            else:
                print("所有重试都失败了")
                return False


async def main():
    """主函数"""
    print("=== MCP SSE 服务器连接测试 ===")
    print("确保服务器已在 http://127.0.0.1:8000 启动")
    print("-" * 50)
    
    success = await test_with_retry()
    
    if success:
        print("\n✅ 测试完成，服务器工作正常！")
    else:
        print("\n❌ 测试失败，请检查服务器状态")


if __name__ == "__main__":
    asyncio.run(main())
