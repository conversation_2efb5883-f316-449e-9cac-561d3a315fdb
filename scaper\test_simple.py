"""测试简化版服务器"""
import asyncio
from fastmcp import Client


async def test_simple_server():
    """测试简化版服务器功能"""
    print("=== 测试简化版 MCP 服务器 ===")
    print("连接到: http://127.0.0.1:8001/sse")
    print("-" * 40)
    
    try:
        # 连接到简化版服务器
        client = Client("http://127.0.0.1:8001/sse")
        
        async with client:
            print("✅ 成功连接到服务器")
            
            # 等待连接稳定
            await asyncio.sleep(1)
            
            # 测试连接
            print("\n1. 测试连接...")
            test_result = await client.call_tool("test_connection", {})
            print(f"   结果: {test_result[0].text}")
            
            # 测试服务器信息
            print("\n2. 获取服务器信息...")
            info_result = await client.call_tool("get_server_info", {})
            print(f"   结果: {info_result[0].text}")
            
            # 测试基础爬取
            print("\n3. 测试基础网页爬取...")
            scrape_result = await client.call_tool("scrape_basic", {
                "url": "https://httpbin.org/html"
            })
            result_text = scrape_result[0].text
            print(f"   结果长度: {len(result_text)} 字符")
            print(f"   前100字符: {result_text[:100]}...")
            
            print("\n🎉 所有测试通过！简化版服务器工作正常。")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print("\n请确保服务器已启动：uv run python simple_server.py")


if __name__ == "__main__":
    asyncio.run(test_simple_server())
