"""MCP 客户端使用示例"""
import asyncio
import json
from fastmcp import Client


async def example_basic_scraping():
    """基础网页爬取示例"""
    print("=== 基础网页爬取示例 ===")
    
    # 连接到 SSE 服务器
    client = Client("http://127.0.0.1:8000/sse")
    
    async with client:
        # 爬取网页基本信息
        result = await client.call_tool("scrape_webpage_tool", {
            "url": "https://httpbin.org/html",
            "include_images": True,
            "analyze_images": False
        })
        
        print(f"状态: {result[0].text}")


async def example_image_analysis():
    """图片分析示例"""
    print("\n=== 图片分析示例 ===")
    
    client = Client("http://127.0.0.1:8000/sse")
    
    async with client:
        # 分析单张图片
        result = await client.call_tool("analyze_image_url", {
            "image_url": "https://httpbin.org/image/jpeg",
            "analysis_prompt": "请详细描述这张图片的内容，包括颜色、形状和任何可见的元素"
        })
        
        print(f"图片分析结果: {result[0].text}")


async def example_webpage_with_images():
    """网页爬取 + 图片分析示例"""
    print("\n=== 网页爬取 + 图片分析示例 ===")
    
    client = Client("http://127.0.0.1:8000/sse")
    
    async with client:
        # 爬取网页并分析图片
        result = await client.call_tool("scrape_webpage_tool", {
            "url": "https://httpbin.org/html",
            "include_images": True,
            "analyze_images": True,
            "image_analysis_prompt": "请描述图片中的主要内容和视觉元素"
        })
        
        print(f"网页爬取和图片分析结果: {result[0].text}")


async def example_server_status():
    """服务器状态检查示例"""
    print("\n=== 服务器状态检查 ===")
    
    client = Client("http://127.0.0.1:8000/sse")
    
    async with client:
        # 获取服务器状态
        result = await client.call_tool("get_server_status", {})
        print(f"服务器状态: {result[0].text}")


async def main():
    """主函数 - 运行所有示例"""
    print("MCP Web Scraper 客户端示例")
    print("确保服务器已在 http://127.0.0.1:8000 启动")
    print("-" * 50)
    
    try:
        # 检查服务器状态
        await example_server_status()
        
        # 基础爬取
        await example_basic_scraping()
        
        # 图片分析（需要配置 API 密钥）
        try:
            await example_image_analysis()
        except Exception as e:
            print(f"图片分析示例失败（可能需要配置 API 密钥）: {e}")
        
        # 网页 + 图片分析
        try:
            await example_webpage_with_images()
        except Exception as e:
            print(f"网页图片分析示例失败（可能需要配置 API 密钥）: {e}")
        
        print("\n所有示例运行完成！")
        
    except Exception as e:
        print(f"连接服务器失败: {e}")
        print("请确保服务器已启动：python server.py")


if __name__ == "__main__":
    asyncio.run(main())
