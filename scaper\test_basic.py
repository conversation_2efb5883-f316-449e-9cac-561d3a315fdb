"""基础功能测试"""
import asyncio
from scraper import scrape_webpage


async def test_basic_scraping():
    """测试基础网页爬取功能"""
    print("测试基础网页爬取功能...")
    
    # 测试一个简单的网页
    test_url = "https://httpbin.org/html"
    
    try:
        result = await scrape_webpage(
            url=test_url,
            include_images=True,
            analyze_images=False  # 不分析图片
        )
        
        print(f"爬取状态: {result.get('status')}")
        print(f"页面标题: {result.get('title')}")
        print(f"文本内容长度: {len(result.get('text_content', ''))}")
        print(f"链接数量: {len(result.get('links', []))}")
        print(f"图片数量: {len(result.get('images', []))}")
        
        if result.get('status') == 'success':
            print("✅ 基础爬取功能正常")
        else:
            print(f"❌ 爬取失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_basic_scraping())
