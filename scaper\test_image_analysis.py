"""测试图片分析功能"""
import asyncio
from fastmcp import Client


async def test_image_analysis_features():
    """测试所有图片分析功能"""
    print("=== 测试图片分析功能 ===")
    print("连接到: http://127.0.0.1:8000/sse")
    print("-" * 50)
    
    # 测试图片URL（使用公开的测试图片）
    test_image_urls = [
        "https://httpbin.org/image/jpeg",
        "https://httpbin.org/image/png",
        "https://httpbin.org/image/svg"
    ]
    
    try:
        client = Client("http://127.0.0.1:8000/sse")
        
        async with client:
            print("✅ 成功连接到服务器")
            await asyncio.sleep(1)
            
            # 1. 测试服务器状态
            print("\n1. 检查服务器状态...")
            status_result = await client.call_tool("get_server_status", {})
            status_data = eval(status_result[0].text)  # 简单解析，生产环境应使用 json.loads
            
            print(f"   服务器版本: {status_data.get('version')}")
            print(f"   图片分析可用: {status_data.get('image_analysis', {}).get('available', False)}")
            
            if not status_data.get('image_analysis', {}).get('available', False):
                print("⚠️  图片分析功能不可用，请配置 DOUBAO_API_KEY")
                print("   继续测试其他功能...")
            
            # 2. 测试单张图片分析
            print("\n2. 测试单张图片分析...")
            try:
                image_result = await client.call_tool("analyze_image_url", {
                    "image_url": test_image_urls[0],
                    "analysis_prompt": "请描述这张图片的内容、颜色和风格"
                })
                print(f"   结果: {image_result[0].text[:100]}...")
            except Exception as e:
                print(f"   ❌ 单张图片分析失败: {str(e)}")
            
            # 3. 测试批量图片分析
            print("\n3. 测试批量图片分析...")
            try:
                batch_result = await client.call_tool("analyze_multiple_image_urls", {
                    "image_urls": test_image_urls[:2],  # 只测试前两张
                    "analysis_prompt": "请简要描述这张图片"
                })
                print(f"   结果: {batch_result[0].text[:100]}...")
            except Exception as e:
                print(f"   ❌ 批量图片分析失败: {str(e)}")
            
            # 4. 测试多问题图片分析
            print("\n4. 测试多问题图片分析...")
            try:
                questions = [
                    "这张图片的主要颜色是什么？",
                    "图片中有什么形状？",
                    "这张图片给人什么感觉？"
                ]
                
                multi_q_result = await client.call_tool("analyze_image_with_custom_questions", {
                    "image_url": test_image_urls[0],
                    "questions": questions
                })
                print(f"   结果: {multi_q_result[0].text[:100]}...")
            except Exception as e:
                print(f"   ❌ 多问题图片分析失败: {str(e)}")
            
            # 5. 测试网页图片分析
            print("\n5. 测试网页图片分析...")
            try:
                webpage_result = await client.call_tool("analyze_webpage_images", {
                    "url": "https://httpbin.org/html",
                    "analysis_prompt": "描述网页中的图片内容"
                })
                print(f"   结果: {webpage_result[0].text[:100]}...")
            except Exception as e:
                print(f"   ❌ 网页图片分析失败: {str(e)}")
            
            # 6. 测试完整网页爬取+图片分析
            print("\n6. 测试完整网页爬取+图片分析...")
            try:
                full_result = await client.call_tool("scrape_webpage_tool", {
                    "url": "https://httpbin.org/html",
                    "include_images": True,
                    "analyze_images": True,
                    "image_analysis_prompt": "分析图片内容和用途"
                })
                print(f"   结果: {full_result[0].text[:100]}...")
            except Exception as e:
                print(f"   ❌ 完整爬取+分析失败: {str(e)}")
            
            print("\n🎉 图片分析功能测试完成！")
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        print("\n请确保服务器已启动：uv run python server.py")


async def test_image_analysis_with_real_api():
    """测试真实API的图片分析功能（需要配置API密钥）"""
    print("\n=== 测试真实API图片分析 ===")
    
    # 使用一些有意义的测试图片
    test_cases = [
        {
            "url": "https://httpbin.org/image/jpeg",
            "prompt": "请详细描述这张图片，包括颜色、形状、内容等"
        }
    ]
    
    try:
        client = Client("http://127.0.0.1:8000/sse")
        
        async with client:
            for i, test_case in enumerate(test_cases):
                print(f"\n测试案例 {i+1}: {test_case['url']}")
                
                try:
                    result = await client.call_tool("analyze_image_url", {
                        "image_url": test_case["url"],
                        "analysis_prompt": test_case["prompt"]
                    })
                    
                    print(f"分析结果: {result[0].text}")
                    
                except Exception as e:
                    print(f"❌ 分析失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")


async def main():
    """主测试函数"""
    print("图片分析功能测试套件")
    print("=" * 60)
    
    # 基础功能测试
    await test_image_analysis_features()
    
    # 如果配置了API密钥，进行真实API测试
    print("\n" + "=" * 60)
    response = input("是否测试真实API功能？(需要配置API密钥) [y/N]: ")
    if response.lower() == 'y':
        await test_image_analysis_with_real_api()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
