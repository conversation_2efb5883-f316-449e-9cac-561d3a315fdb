"""图片分析模块 - 使用火山引擎豆包模型"""
import base64
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from io import BytesIO
from PIL import Image
import logging

from config import config

logger = logging.getLogger(__name__)


class ImageAnalyzer:
    """图片分析器，使用火山引擎豆包模型"""
    
    def __init__(self):
        self.api_key = config.DOUBAO_API_KEY
        self.base_url = config.DOUBAO_BASE_URL
        self.model = config.DOUBAO_MODEL
        
    async def analyze_image(self, image_data: bytes, prompt: str = "请详细描述这张图片的内容") -> str:
        """
        分析单张图片
        
        Args:
            image_data: 图片的二进制数据
            prompt: 分析提示词
            
        Returns:
            图片分析结果
        """
        try:
            # 将图片转换为base64
            base64_image = self._encode_image_to_base64(image_data)
            
            # 构建请求数据
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7
            }
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status}, {error_text}")
                        return f"图片分析失败: {response.status}"
                        
        except Exception as e:
            logger.error(f"Image analysis error: {str(e)}")
            return f"图片分析出错: {str(e)}"
    
    async def analyze_multiple_images(
        self, 
        images_data: List[bytes], 
        prompt: str = "请分别描述这些图片的内容"
    ) -> List[str]:
        """
        分析多张图片
        
        Args:
            images_data: 图片数据列表
            prompt: 分析提示词
            
        Returns:
            图片分析结果列表
        """
        tasks = []
        for i, image_data in enumerate(images_data):
            individual_prompt = f"{prompt} (图片 {i+1})"
            tasks.append(self.analyze_image(image_data, individual_prompt))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(f"图片 {i+1} 分析失败: {str(result)}")
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _encode_image_to_base64(self, image_data: bytes) -> str:
        """将图片数据编码为base64字符串"""
        try:
            # 使用PIL验证和处理图片
            image = Image.open(BytesIO(image_data))
            
            # 如果图片过大，进行压缩
            max_size = (1024, 1024)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 转换为RGB模式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 保存为JPEG格式的字节流
            buffer = BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            buffer.seek(0)
            
            # 编码为base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Image encoding error: {str(e)}")
            # 如果PIL处理失败，直接编码原始数据
            return base64.b64encode(image_data).decode('utf-8')


# 全局图片分析器实例
image_analyzer = ImageAnalyzer()
