[project]
name = "mcp-web-scraper"
version = "0.1.0"
description = "MCP SSE server for web scraping with image analysis"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
dependencies = [
    "fastmcp>=0.1.0",
    "playwright>=1.40.0",
    "requests>=2.31.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "aiohttp>=3.9.0",
    "beautifulsoup4>=4.12.0",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88
