"""测试 MCP 服务器功能"""
import asyncio
import logging
from fastmcp import Client

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_server():
    """测试服务器功能"""
    
    # 连接到本地服务器
    client = Client("server.py")
    
    try:
        async with client:
            logger.info("连接到 MCP 服务器成功")
            
            # 测试服务器状态
            logger.info("测试服务器状态...")
            status = await client.call_tool("get_server_status", {})
            logger.info(f"服务器状态: {status}")
            
            # 测试基础网页爬取
            logger.info("测试基础网页爬取...")
            test_url = "https://httpbin.org/html"
            
            scrape_result = await client.call_tool("scrape_webpage_tool", {
                "url": test_url,
                "include_images": True,
                "analyze_images": False
            })
            
            logger.info(f"爬取结果状态: {scrape_result.get('status')}")
            logger.info(f"页面标题: {scrape_result.get('title')}")
            logger.info(f"文本长度: {len(scrape_result.get('text_content', ''))}")
            logger.info(f"链接数量: {len(scrape_result.get('links', []))}")
            logger.info(f"图片数量: {len(scrape_result.get('images', []))}")
            
            # 测试图片URL分析（使用一个公开的测试图片）
            logger.info("测试单张图片分析...")
            test_image_url = "https://httpbin.org/image/png"
            
            image_result = await client.call_tool("analyze_image_url", {
                "image_url": test_image_url,
                "analysis_prompt": "请描述这张图片的内容"
            })
            
            logger.info(f"图片分析状态: {image_result.get('status')}")
            if image_result.get('status') == 'success':
                logger.info(f"图片分析结果: {image_result.get('analysis')}")
            else:
                logger.warning(f"图片分析失败: {image_result.get('error')}")
            
            logger.info("所有测试完成！")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_server())
