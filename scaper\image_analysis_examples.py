"""图片分析功能使用示例"""
import asyncio
from fastmcp import Client


async def example_single_image_analysis():
    """示例1: 单张图片分析"""
    print("=== 示例1: 单张图片分析 ===")
    
    client = Client("http://127.0.0.1:8000/sse")
    
    async with client:
        # 分析单张图片
        result = await client.call_tool("analyze_image_url", {
            "image_url": "https://httpbin.org/image/jpeg",
            "analysis_prompt": "请详细描述这张图片的内容、颜色、构图和可能的用途"
        })
        
        print("分析结果:")
        print(result[0].text)


async def example_batch_image_analysis():
    """示例2: 批量图片分析"""
    print("\n=== 示例2: 批量图片分析 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 批量分析多张图片
        image_urls = [
            "https://httpbin.org/image/jpeg",
            "https://httpbin.org/image/png",
            "https://httpbin.org/image/svg"
        ]
        
        result = await client.call_tool("analyze_multiple_image_urls", {
            "image_urls": image_urls,
            "analysis_prompt": "请简要描述这张图片的主要特征"
        })
        
        print("批量分析结果:")
        print(result[0].text)


async def example_custom_questions():
    """示例3: 自定义问题分析"""
    print("\n=== 示例3: 自定义问题分析 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 使用自定义问题分析图片
        questions = [
            "这张图片的主要颜色是什么？",
            "图片中有哪些几何形状？",
            "这张图片可能用于什么场景？",
            "图片的整体风格是什么？",
            "如果要改进这张图片，你有什么建议？"
        ]
        
        result = await client.call_tool("analyze_image_with_custom_questions", {
            "image_url": "https://httpbin.org/image/jpeg",
            "questions": questions
        })
        
        print("多问题分析结果:")
        print(result[0].text)


async def example_webpage_image_analysis():
    """示例4: 网页图片分析"""
    print("\n=== 示例4: 网页图片分析 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 分析网页中的所有图片
        result = await client.call_tool("analyze_webpage_images", {
            "url": "https://httpbin.org/html",
            "analysis_prompt": "分析这张图片在网页中的作用和内容"
        })
        
        print("网页图片分析结果:")
        print(result[0].text)


async def example_comprehensive_webpage_scraping():
    """示例5: 综合网页爬取+图片分析"""
    print("\n=== 示例5: 综合网页爬取+图片分析 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 完整的网页爬取，包含图片分析
        result = await client.call_tool("scrape_webpage_tool", {
            "url": "https://httpbin.org/html",
            "include_images": True,
            "analyze_images": True,
            "image_analysis_prompt": "分析图片内容，并说明它与网页主题的关系"
        })
        
        print("综合爬取+分析结果:")
        print(result[0].text)


async def example_specialized_analysis():
    """示例6: 专业化图片分析"""
    print("\n=== 示例6: 专业化图片分析 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 不同类型的专业化分析
        analysis_scenarios = [
            {
                "name": "商品分析",
                "prompt": "假设这是一个商品图片，请分析其卖点、特色和可能的目标客户群体"
            },
            {
                "name": "设计分析",
                "prompt": "从设计角度分析这张图片的构图、色彩搭配和视觉效果"
            },
            {
                "name": "技术分析",
                "prompt": "从技术角度分析这张图片的质量、分辨率和可能的制作方式"
            }
        ]
        
        for scenario in analysis_scenarios:
            print(f"\n{scenario['name']}:")
            try:
                result = await client.call_tool("analyze_image_url", {
                    "image_url": "https://httpbin.org/image/jpeg",
                    "analysis_prompt": scenario["prompt"]
                })
                print(result[0].text)
            except Exception as e:
                print(f"分析失败: {str(e)}")


async def example_error_handling():
    """示例7: 错误处理演示"""
    print("\n=== 示例7: 错误处理演示 ===")
    
    client = Client("http://127.0.0.1:8001/sse")
    
    async with client:
        # 测试无效URL
        print("测试无效图片URL:")
        try:
            result = await client.call_tool("analyze_image_url", {
                "image_url": "https://invalid-url.com/nonexistent.jpg",
                "analysis_prompt": "分析这张图片"
            })
            print(result[0].text)
        except Exception as e:
            print(f"预期的错误: {str(e)}")
        
        # 测试无API密钥情况
        print("\n测试API密钥检查:")
        try:
            result = await client.call_tool("get_server_status", {})
            print("服务器状态检查完成")
        except Exception as e:
            print(f"状态检查失败: {str(e)}")


async def main():
    """运行所有示例"""
    print("🖼️ 图片分析功能使用示例")
    print("=" * 60)
    print("确保服务器已启动: uv run python server.py")
    print("如需完整功能，请配置 DOUBAO_API_KEY")
    print("=" * 60)
    
    examples = [
        ("单张图片分析", example_single_image_analysis),
        ("批量图片分析", example_batch_image_analysis),
        ("自定义问题分析", example_custom_questions),
        ("网页图片分析", example_webpage_image_analysis),
        ("综合网页爬取", example_comprehensive_webpage_scraping),
        ("专业化分析", example_specialized_analysis),
        ("错误处理", example_error_handling)
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n{'='*20} {name} {'='*20}")
            await example_func()
            print("✅ 示例完成")
        except Exception as e:
            print(f"❌ 示例失败: {str(e)}")
        
        # 在示例之间暂停
        await asyncio.sleep(1)
    
    print("\n🎉 所有示例运行完成！")
    print("\n💡 提示:")
    print("- 配置 DOUBAO_API_KEY 以启用完整的图片分析功能")
    print("- 可以根据需要修改分析提示词")
    print("- 支持多种图片格式 (JPEG, PNG, SVG等)")
    print("- 批量分析时会自动处理失败的图片")


if __name__ == "__main__":
    asyncio.run(main())
