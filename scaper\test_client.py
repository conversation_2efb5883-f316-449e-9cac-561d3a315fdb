"""测试 MCP 客户端连接"""
import asyncio
from fastmcp import Client


async def test_client_connection():
    """测试客户端连接到 SSE 服务器"""
    print("测试连接到 MCP SSE 服务器...")
    
    # 连接到 SSE 服务器
    client = Client("http://127.0.0.1:8000/sse")
    
    try:
        async with client:
            print("✅ 成功连接到服务器")
            
            # 测试服务器状态
            print("\n测试服务器状态...")
            status_result = await client.call_tool("get_server_status", {})
            print(f"服务器状态: {status_result[0].text}")
            
            # 测试基础网页爬取
            print("\n测试基础网页爬取...")
            scrape_result = await client.call_tool("scrape_webpage_tool", {
                "url": "https://httpbin.org/html",
                "include_images": True,
                "analyze_images": False
            })
            print(f"爬取结果: {scrape_result[0].text[:200]}...")
            
            print("\n✅ 所有测试通过！")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_client_connection())
